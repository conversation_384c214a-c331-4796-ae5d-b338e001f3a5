"use client";

import { useEffect, useRef, useState } from 'react';
import BackupContactForm from './BackupContactForm';

const FreeEstimateForm = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [showBackupForm, setShowBackupForm] = useState(false);

  useEffect(() => {
    const formId = 'free-estimate-form-afcau9';
    const allowedDomain = 'opnform.com';

    // Set a timeout to show error if loading takes too long
    const timeoutId = setTimeout(() => {
      console.warn('OpnForm script loading timeout - showing fallback');
      setHasError(true);
      setIsLoading(false);
    }, 10000); // 10 second timeout

    // Check if the script has already been added to avoid duplicates
    if (document.getElementById('opnform-widget-script')) {
      clearTimeout(timeoutId);
      try {
        if (typeof (window as any).initEmbed === 'function') {
          (window as any).initEmbed(formId);
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize existing OpnForm embed:', error);
        setHasError(true);
        setIsLoading(false);
      }
      return;
    }

    // Try to load the iframe directly first (fallback approach)
    const testIframe = () => {
      const iframe = document.getElementById(formId) as HTMLIFrameElement;
      if (iframe) {
        iframe.onload = () => {
          clearTimeout(timeoutId);
          setIsLoading(false);
        };
        iframe.onerror = () => {
          console.warn('Direct iframe loading failed, trying script approach');
          loadScript();
        };
        // If iframe loads successfully, we don't need the script
        return;
      }
      loadScript();
    };

    const loadScript = () => {
      const script = document.createElement('script');
      script.id = 'opnform-widget-script';
      script.type = 'text/javascript';
      script.src = `https://${allowedDomain}/widgets/iframe.min.js`;
      script.crossOrigin = 'anonymous';

      script.onload = () => {
        clearTimeout(timeoutId);
        try {
          if (typeof (window as any).initEmbed === 'function') {
            (window as any).initEmbed(formId);
          }
          setIsLoading(false);
        } catch (error) {
          console.error('Failed to initialize OpnForm embed:', error);
          setHasError(true);
          setIsLoading(false);
        }
      };

      script.onerror = (error) => {
        clearTimeout(timeoutId);
        console.error('Failed to load OpnForm script:', error);
        setHasError(true);
        setIsLoading(false);
      };

      document.head.appendChild(script);
    };

    // Start with testing iframe directly
    testIframe();

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  return (
    <div ref={containerRef}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A6C6F] mx-auto mb-4"></div>
            <p className="text-gray-600">Loading contact form...</p>
          </div>
        </div>
      )}

      {hasError && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="text-center mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Get Your Free Estimate</h3>
            <p className="text-gray-700">Contact us directly for a free estimate on your project.</p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="bg-[#4A6C6F] text-white rounded-lg p-4 mb-3">
                <svg className="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <p className="font-semibold">Call Us</p>
              </div>
              <a href="tel:************" className="text-[#4A6C6F] hover:underline text-lg font-semibold">
                ************
              </a>
              <p className="text-sm text-gray-600 mt-1">Mon-Fri 8AM-6PM</p>
            </div>

            <div className="text-center">
              <div className="bg-[#4A6C6F] text-white rounded-lg p-4 mb-3">
                <svg className="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <p className="font-semibold">Email Us</p>
              </div>
              <a href="mailto:<EMAIL>" className="text-[#4A6C6F] hover:underline text-lg font-semibold">
                <EMAIL>
              </a>
              <p className="text-sm text-gray-600 mt-1">We respond within 24 hours</p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-white rounded-lg border">
            <h4 className="font-semibold text-gray-900 mb-2">Services We Provide:</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Yard Grading & Land Leveling</li>
              <li>• Excavation Services</li>
              <li>• Hardscaping & Landscaping</li>
              <li>• Drainage Solutions</li>
            </ul>
          </div>

          <div className="mt-4 text-center space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="bg-[#4A6C6F] text-white px-4 py-2 rounded-lg hover:bg-[#3A5C5F] transition-colors"
            >
              Try Loading Form Again
            </button>
            <button
              onClick={() => setShowBackupForm(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Use Simple Contact Form
            </button>
          </div>
        </div>
      )}

      {!hasError && (
        <iframe
          style={{
            border: 'none',
            width: '100%',
            height: '700px',
            display: isLoading ? 'none' : 'block'
          }}
          id="free-estimate-form-afcau9"
          src="https://opnform.com/forms/free-estimate-form-afcau9"
          title="Free Estimate Form"
          sandbox="allow-scripts allow-forms allow-same-origin"
          loading="lazy"
        />
      )}

      {showBackupForm && (
        <div className="mt-6">
          <BackupContactForm />
        </div>
      )}
    </div>
  );
};

export default FreeEstimateForm;