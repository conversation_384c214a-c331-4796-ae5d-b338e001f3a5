"use client"

import { useState } from "react"
import Link from "next/link"
import { Phone, Menu, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="bg-[#F3F1E7] shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img
              src="/excavation-leveling-grinding-3s-builder-logo.png"
              alt="3S Builder Solution Logo"
              className="h-16 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-[#4A6C6F] font-medium">
              Home
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center">
                  <Link href="/services" className="text-gray-700 hover:text-[#4A6C6F] font-medium mr-1">
                    Services
                  </Link>
                  <button className="text-gray-700 hover:text-[#4A6C6F] p-1">
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-48">
                <DropdownMenuItem asChild>
                  <Link href="/services" className="w-full font-medium text-[#4A6C6F]">
                    View All Services
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/services/yard-grading" className="w-full">
                    Yard Grading
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/services/excavation" className="w-full">
                    Excavation
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/services/land-leveling" className="w-full">
                    Land Leveling
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/services/hardscaping" className="w-full">
                    Hardscaping
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/services/landscaping" className="w-full">
                    Landscaping
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Link href="/locations" className="text-gray-700 hover:text-[#4A6C6F] font-medium">
              Locations
            </Link>


            <Link href="/about-us" className="text-gray-700 hover:text-[#4A6C6F] font-medium">
              About Us
            </Link>

            <Link href="/contact-us" className="text-gray-700 hover:text-[#4A6C6F] font-medium">
              Contact Us
            </Link>
          </nav>

          {/* Phone and CTA */}
          <div className="hidden lg:flex items-center space-x-4">
            <a href="tel:4132414577" className="flex items-center text-gray-700 hover:text-[#4A6C6F]">
              <Phone className="h-5 w-5 mr-2" />
              <span className="font-medium">************</span>
            </a>
            <Button asChild className="bg-[#709CA7] hover:bg-[#4A6C6F] text-white">
              <Link href="/contact-us">Get Free Estimate</Link>
            </Button>
          </div>

          {/* Mobile actions */}
          <div className="lg:hidden flex items-center space-x-2">
            {/* Mobile phone number - only show on larger mobile screens */}
            <div className="hidden sm:block">
              <a href="tel:4132414577" className="flex items-center text-gray-700 hover:text-[#4A6C6F]">
                <Phone className="h-5 w-5 mr-1" />
                <span className="font-medium text-sm">************</span>
              </a>
            </div>

            {/* Mobile phone icon only - show on small screens */}
            <div className="sm:hidden">
              <a href="tel:4132414577" className="flex items-center justify-center p-2 text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80 rounded-md">
                <Phone className="h-5 w-5" />
              </a>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80 focus:outline-none"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="lg:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link
              href="/"
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
              onClick={toggleMenu}
            >
              Home
            </Link>

            <div className="relative">
              <div className="flex items-center">
                <Link
                  href="/services"
                  className="flex-1 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  Services
                </Link>
                <button className="px-3 py-2 text-gray-700 hover:text-[#4A6C6F]">
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
              <div className="pl-6 space-y-1">
                <Link
                  href="/services"
                  className="block px-3 py-2 rounded-md text-base font-medium text-[#4A6C6F] hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  View All Services
                </Link>
                <Link
                  href="/services/yard-grading"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  Yard Grading
                </Link>
                <Link
                  href="/services/excavation"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  Excavation
                </Link>
                <Link
                  href="/services/land-leveling"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  Land Leveling
                </Link>
                <Link
                  href="/services/hardscaping"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  Hardscaping
                </Link>
                <Link
                  href="/services/landscaping"
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
                  onClick={toggleMenu}
                >
                  Landscaping
                </Link>
              </div>
            </div>

            <Link
              href="/locations"
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
              onClick={toggleMenu}
            >
              Locations
            </Link>


            <Link
              href="/about-us"
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
              onClick={toggleMenu}
            >
              About Us
            </Link>

            <Link
              href="/contact-us"
              className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-[#4A6C6F] hover:bg-[#F3F1E7]/80"
              onClick={toggleMenu}
            >
              Contact Us
            </Link>

            {/* Call to action section */}
            <div className="px-3 py-4 border-t border-gray-200 mt-2">
              <div className="space-y-3">
                <a
                  href="tel:4132414577"
                  className="flex items-center justify-center w-full px-4 py-3 bg-[#4A6C6F] text-white rounded-md hover:bg-[#3a5a5d] transition-colors"
                  onClick={toggleMenu}
                >
                  <Phone className="h-5 w-5 mr-2" />
                  <span className="font-medium">Call ************</span>
                </a>

                <Button asChild className="w-full bg-[#709CA7] hover:bg-[#4A6C6F] text-white" onClick={toggleMenu}>
                  <Link href="/contact-us">Get Free Estimate</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
