import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export const metadata = {
  title: "Professional Outdoor Services | 3S Builder Solution",
  description:
    "Comprehensive outdoor services including yard grading, excavation, hardscaping, landscaping, and land leveling in Southeastern Pennsylvania.",
}

export default function ServicesPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative rounded-lg overflow-hidden mb-12">
        <div className="absolute inset-0 z-0">
          <Image
            src="/Transform_Your_Outdoor_Space__Professional_grading_excavation_hardscaping_and_landscaping_servic.webp"
            alt="Professional outdoor services including yard grading, excavation, hardscaping, and landscaping by 3S Builder Solution in Southeastern Pennsylvania"
            fill
            className="object-cover opacity-80"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        <div className="relative z-10 p-8 md:p-12 lg:p-16 text-white">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            Professional Outdoor Services
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mb-8">
            Transform your outdoor space with our comprehensive range of professional services. From ground preparation to beautiful finishing touches, we provide end-to-end solutions for your property in Southeastern Pennsylvania.
          </p>
          <Button asChild size="lg" className="bg-[#709CA7] hover:bg-[#4A6C6F]">
            <Link href="/contact-us">Get Your Free Estimate</Link>
          </Button>
        </div>
      </div>

      {/* Services Overview */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Comprehensive Services</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We specialize in transforming outdoor spaces with professional expertise and attention to detail. Each service is designed to enhance your property's functionality, beauty, and value.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {[
            {
              title: "Yard Grading",
              description:
                "Professional yard grading services to ensure proper drainage, foundation preparation, and optimal slopes for your outdoor projects.",
              image:
                "/yard_grading__Professional_land_yard_grading_services_to_ensure_proper_drainage_and_foundation_for_.webp",
              link: "/services/yard-grading",
              features: ["Drainage Solutions", "Foundation Preparation", "Slope Optimization", "Site Preparation"],
            },
            {
              title: "Excavation",
              description:
                "Expert excavation services for foundations, utilities, drainage systems, and site preparation with professional equipment and techniques.",
              image:
                "/Excavation__Expert_excavation_services_for_foundations_pools_and_other_outdoor_features_(1).webp",
              link: "/services/excavation",
              features: ["Foundation Excavation", "Utility Installation", "Drainage Systems", "Site Clearing"],
            },
            {
              title: "Land Leveling",
              description:
                "Precise land leveling to create perfect surfaces for patios, walkways, lawns, and other outdoor features.",
              image:
                "/lawn_leveling__Expert_ground_lawn_leveling_to_create_the_perfect_base_for_patios_walkways_and_oth.webp",
              link: "/services/land-leveling",
              features: ["Patio Preparation", "Lawn Leveling", "Surface Grading", "Base Creation"],
            },
            {
              title: "Hardscaping",
              description:
                "Beautiful and durable hardscape installations including patios, walkways, retaining walls, and outdoor living spaces.",
              image:
                "/Hardscaping__Beautiful_and_durable_hardscape_installations_including_patios_walkways_retaining_wa_(1).webp",
              link: "/services/hardscaping",
              features: ["Patio Installation", "Walkways", "Retaining Walls", "Outdoor Kitchens"],
            },
            {
              title: "Landscaping",
              description:
                "Complete landscaping services including design, sod installation, planting, mulching, and ongoing maintenance.",
              image:
                "/Landscaping__Complete_landscaping_services_including_sod_installation_planting_mulching_and_land_(1).webp",
              link: "/services/landscaping",
              features: ["Landscape Design", "Sod Installation", "Plant Installation", "Mulching"],
            },
          ].map((service, index) => (
            <Link key={index} href={service.link} className="group">
              <Card className="overflow-hidden hover:shadow-lg transition-shadow h-full">
                <div className="relative h-48">
                  <Image
                    src={service.image || "/placeholder.svg"}
                    alt={`Professional ${service.title.toLowerCase()} services by 3S Builder Solution in Southeastern Pennsylvania`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 group-hover:text-[#4A6C6F] transition-colors mb-3">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2 mb-4">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-700">
                        <div className="flex-shrink-0 h-4 w-4 mr-2 text-[#4A6C6F]">
                          <svg fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <span className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
                    Learn More About {service.title} →
                  </span>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>

      {/* Why Choose Our Services */}
      <div className="mb-16 bg-[#F3F1E7] rounded-lg p-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose 3S Builder Solution</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We bring years of experience, professional equipment, and a commitment to quality that sets us apart in Southeastern Pennsylvania.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              title: "Professional Expertise",
              description: "Our experienced team brings years of knowledge and skill to every project, ensuring quality results.",
              icon: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z",
            },
            {
              title: "Quality Equipment",
              description: "We invest in professional-grade equipment to ensure efficient, precise work on projects of any size.",
              icon: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z",
            },
            {
              title: "Local Knowledge",
              description: "We understand the unique soil conditions, regulations, and challenges of Southeastern Pennsylvania properties.",
              icon: "M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z",
            },
            {
              title: "Comprehensive Solutions",
              description: "From initial grading to final landscaping, we provide complete outdoor transformation services.",
              icon: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",
            },
            {
              title: "Customer Satisfaction",
              description: "Your satisfaction is our priority. We work closely with you throughout the project to exceed expectations.",
              icon: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",
            },
            {
              title: "Free Estimates",
              description: "We provide detailed, no-obligation estimates so you know exactly what to expect before work begins.",
              icon: "M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z",
            },
          ].map((benefit, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6">
              <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-[#709CA7]/20 text-[#4A6C6F] mb-4">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={benefit.icon} />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">{benefit.title}</h3>
              <p className="text-gray-700">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Service Areas */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Serving Southeastern Pennsylvania</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We provide our professional outdoor services throughout the region, bringing expertise and quality to communities across Southeastern Pennsylvania.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
          {[
            "Philadelphia", "Bensalem", "Abington", "Warminster", 
            "Mount Airy", "Horsham", "Springfield Township", "Lansdale",
            "Chestnut Hill", "Southampton", "Willow Grove", "Bristol",
            "Hatboro"
          ].map((location, index) => (
            <Link
              key={index}
              href={`/locations/${location.toLowerCase().replace(" ", "-")}-pa`}
              className="bg-[#F3F1E7] p-3 rounded-lg text-center text-gray-700 hover:bg-[#4A6C6F] hover:text-white transition-colors"
            >
              {location}, PA
            </Link>
          ))}
        </div>

        <div className="text-center">
          <Button asChild variant="outline" className="text-[#4A6C6F] border-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/locations">View All Service Areas</Link>
          </Button>
        </div>
      </div>

      {/* CTA */}
      <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Transform Your Outdoor Space?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Contact 3S Builder Solution today for a free consultation and estimate. Our professional team is ready to help
          you create the outdoor space of your dreams.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/contact-us">Get Your Free Estimate</Link>
          </Button>
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        </div>
      </div>
    </div>
  )
}
