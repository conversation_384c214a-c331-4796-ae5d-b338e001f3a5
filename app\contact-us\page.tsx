import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Phone, MapPin } from "lucide-react"

export const metadata = {
  title: "Contact Us | 3S Builder Solution",
  description:
    "Contact 3S Builder Solution for a free estimate on yard-grading, excavation, land-leveling, hardscaping, and landscaping services in Southeastern Pennsylvania.",
  }

export default function ContactPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">Contact 3S Builder Solution</h1>
        <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
          Reach out to discuss your project and get a free estimate. We're here to help transform your outdoor space.
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-12 items-start">
        {/* Contact Form */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          
          <div className="w-full">
            <iframe
              style={{border: 'none', width: '100%', minHeight: '700px'}}
              id="free-estimate-form-afcau9"
              src="https://opnform.com/forms/free-estimate-form-afcau9"
            ></iframe>
          </div>
          <script
            type="text/javascript"
            dangerouslySetInnerHTML={{
              __html: `
                (function() {
                  var script = document.createElement('script');
                  script.src = 'https://opnform.com/widgets/iframe.min.js';
                  script.onload = function() {
                    if (typeof initEmbed === 'function') {
                      initEmbed('free-estimate-form-afcau9');
                    }
                  };
                  document.head.appendChild(script);
                })();
              `
            }}
          />
        </div>

        {/* Contact Information */}
        <div>
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            <h2 className="text-2xl font-bold mb-6">Contact Information</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <Phone className="h-6 w-6 text-[#4A6C6F] mr-4 mt-1" />
                <div>
                  <h3 className="font-medium">Phone</h3>
                  <a href="tel:4132414577" className="text-lg hover:text-[#4A6C6F]">
                    ************
                  </a>
                  <p className="text-sm text-gray-500">Available Mon-Fri, 8am-6pm</p>
                </div>
              </div>



              <div className="flex items-start">
                <MapPin className="h-6 w-6 text-[#4A6C6F] mr-4 mt-1" />
                <div>
                  <h3 className="font-medium">Service Area</h3>
                  <p className="text-lg">Southeastern Pennsylvania</p>
                  <p className="text-sm text-gray-500">
                    <Link href="/locations" className="text-[#4A6C6F] hover:underline">
                      View all service areas
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-8">
            <h2 className="text-2xl font-bold mb-6">Why Choose Us</h2>
            <ul className="space-y-4">
              {[
                {
                  title: "Free Estimates",
                  description: "We provide detailed, no-obligation estimates for all projects.",
                },
                {
                  title: "5+ Years Experience",
                  description: "Our team brings years of professional experience to every job.",
                },
                {
                  title: "Professional Equipment",
                  description: "We use professional-grade equipment for efficient, quality results.",
                },
                {
                  title: "Attention to Detail",
                  description: "We take pride in our meticulous approach to every project.",
                },
                {
                  title: "Customer Satisfaction",
                  description: "Your satisfaction is our priority from start to finish.",
                },
              ].map((item, index) => (
                <li key={index} className="flex">
                  <div className="flex-shrink-0 h-6 w-6 mt-1 mr-3 flex items-center justify-center rounded-full bg-[#709CA7]/20 text-[#4A6C6F]">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Service Areas Map - Placeholder */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold mb-6 text-center">Our Service Areas</h2>
        <div className="bg-gray-100 rounded-lg overflow-hidden h-[400px] relative">
          <Image
            src="/Our_Service_Areas__Serving_Southeastern_Pennsylvania.webp"
            alt="3S Builder Solution service areas map"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white bg-opacity-90 p-6 rounded-lg max-w-md text-center">
              <h3 className="text-xl font-bold mb-2">Serving Southeastern Pennsylvania</h3>
              <p className="mb-4">
                We provide our services throughout the region, including Philadelphia, Bensalem, Abington, and
                surrounding areas.
              </p>
              <Button asChild className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
                <Link href="/locations">View All Service Areas</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
