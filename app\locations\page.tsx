import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

export const metadata = {
  title: "Service Areas | 3S Builder Solution",
  description:
    "3S Builder Solution proudly serves communities across Southeastern Pennsylvania including Philadelphia, Bensalem, Abington, and surrounding areas.",
}

export default function LocationsPage() {
  const primaryLocations = [
    "Philadelphia",
    "Bensalem",
    "Abington",
    "Warminster",
    "Mount Airy",
    "Horsham",
    "Springfield Township",
    "Lansdale",
    "Southampton",
    "Willow Grove",
    "Chestnut Hill",
    "Bristol",
    "Hatboro",
  ]

  const secondaryLocations = [
    "Jenkintown",
    "Glenside",
    "Ambler",
    "Fort Washington",
    "Feasterville",
    "Huntingdon Valley",
    "Elkins Park",
    "Cheltenham",
    "Wyncote",
    "Flourtown",
    "Oreland",
    "Dresher",
    "Ivyland",
    "Richboro",
  ]

  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative rounded-lg overflow-hidden mb-12">
        <div className="absolute inset-0 z-0">
          <Image
            src="/Serving_Communities_Across_Southeastern_Pennsylvania__3S_Builder_Solution_proudly_provides_expert_g.webp"
            alt="3S Builder Solution service areas across Southeastern Pennsylvania including Philadelphia, Bensalem, Abington and surrounding communities"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        <div className="relative z-10 p-8 md:p-12 lg:p-16 text-white">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            Serving Communities Across Southeastern Pennsylvania
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mb-8">
            3S Builder Solution proudly provides expert yard grading, excavation, land leveling, hardscaping, and landscaping
            services throughout the region.
          </p>
          <Button asChild size="lg" className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
            <Link href="/contact-us">Contact Us About Your Area</Link>
          </Button>
        </div>
      </div>

      {/* Introduction */}
      <div className="mb-12 text-center max-w-4xl mx-auto">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Our Service Coverage</h2>
        <p className="text-lg text-gray-700">
          3S Builder Solution is proud to serve homeowners and businesses throughout Southeastern Pennsylvania. Our team
          of professionals brings expertise in yard grading, excavation, land leveling, hardscaping, and landscaping to transform
          properties across the region. Whether you're in Philadelphia, the surrounding suburbs, or anywhere in between,
          we're ready to help with your outdoor project needs.
        </p>
      </div>

      {/* Primary Service Areas */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6 text-center">Primary Service Areas</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {primaryLocations.map((location, index) => {
            const slug = location.toLowerCase().replace(" ", "-")
            const imageMap: { [key: string]: string } = {
              "Philadelphia": "/Philadelphia__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_.webp",
              "Bensalem": "/Expert_Grading_Hardscaping__Landscaping_in_Bensalem_PA__Transforming_Bensalem_properties_with_pr.webp",
              "Abington": "/Abington__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_hard.webp",
              "Warminster": "/Warminster__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_ha.webp",
              "Mount Airy": "/Mount_Airy__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_ha.webp",
              "Horsham": "/Horsham__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_hards.webp",
              "Springfield Township": "/Springfield_Township__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_le.webp",
              "Lansdale": "/Lansdale__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_hard.webp",
              "Southampton": "/Southampton__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_h.webp",
              "Willow Grove": "/Willow_Grove__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_.webp",
              "Chestnut Hill": "/Chestnut_Hill__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling.webp",
              "Bristol": "/Bristol__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_hards.webp",
              "Hatboro": "/Hatboro__3S_Builder_Solution_proudly_provides_expert_yard_grading_excavation_lawn_leveling_hards.webp",
            };
            const imageSrc = imageMap[location] || "/placeholder.svg"; // Fallback to placeholder if image not found

            return (
              <Link key={index} href={`/locations/${slug}-pa`} className="group">
                <div className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                  <div className="relative h-40">
                    <Image
                      src={imageSrc}
                      alt={`Professional yard grading, excavation, hardscaping and landscaping services in ${location}, Pennsylvania by 3S Builder Solution`}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-30 group-hover:bg-opacity-20 transition-all"></div>
                  </div>
                  <div className="p-4 text-center">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-[#4A6C6F] transition-colors">
                      {location}, PA
                    </h3>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </div>

      {/* Secondary Service Areas */}
      <div className="mb-12 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold mb-6 text-center">Additional Areas We Serve</h2>
        <p className="text-lg text-gray-700 mb-6 text-center">
          In addition to our primary service areas, we also provide our services to these surrounding communities:
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          {secondaryLocations.map((location, index) => (
            <span key={index} className="px-4 py-2 bg-white rounded-full text-gray-700 shadow-sm">
              {location}, PA
            </span>
          ))}
        </div>
        <p className="mt-6 text-center text-gray-600">
          Don't see your area listed? Contact us to check if we serve your location!
        </p>
      </div>

      {/* Services Overview */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6 text-center">Services Available in All Areas</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              title: "Yard Grading & Excavation",
              description:
              "Professional yard grading and excavation services to prepare your property for construction or solve drainage issues.",
              services: ["Yard Grading", "Excavation", "Site Preparation", "Drainage Solutions"],
              link: "/services/yard-grading",
            },
            {
              title: "Hardscaping",
              description: "Beautiful and functional hardscape installations to enhance your outdoor living space.",
              services: ["Patios", "Walkways", "Retaining Walls", "Outdoor Kitchens", "Fire Pits"],
              link: "/services/hardscaping",
            },
            {
              title: "Landscaping",
              description:
                "Complete landscaping services to beautify your property with plants, trees, and other natural elements.",
              services: ["Sod Installation", "Shrub & Bush Planting", "Mulching", "Small Rocks Installation"],
              link: "/services/landscaping",
            },
          ].map((category, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-xl font-bold mb-2">{category.title}</h3>
              <p className="text-gray-700 mb-4">{category.description}</p>
              <ul className="mb-4 space-y-1">
                {category.services.map((service, serviceIndex) => {
                  // Create service-specific links
                  const serviceLinks: { [key: string]: string } = {
                    "Yard Grading": "/services/yard-grading",
                    "Excavation": "/services/excavation",
                    "Site Preparation": "/services/yard-grading",
                    "Drainage Solutions": "/services/yard-grading",
                    "Patios": "/services/hardscaping",
                    "Walkways": "/services/hardscaping",
                    "Retaining Walls": "/services/hardscaping",
                    "Outdoor Kitchens": "/services/hardscaping",
                    "Fire Pits": "/services/hardscaping",
                    "Sod Installation": "/services/landscaping",
                    "Shrub & Bush Planting": "/services/landscaping",
                    "Mulching": "/services/landscaping",
                    "Small Rocks Installation": "/services/landscaping"
                  };

                  return (
                    <li key={serviceIndex} className="flex items-center">
                      <svg className="h-4 w-4 text-[#4A6C6F] mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <Link
                        href={serviceLinks[service] || category.link}
                        className="text-gray-700 hover:text-[#4A6C6F] transition-colors"
                      >
                        {service}
                      </Link>
                    </li>
                  );
                })}
              </ul>
              <Link href={category.link} className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
                Learn more →
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* CTA */}
      <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Transform Your Property?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Contact 3S Builder Solution today to discuss your project needs in your area. We offer free consultations and
          estimates throughout our service region.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/contact-us">Contact Us Today</Link>
          </Button>
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        </div>
      </div>
    </div>
  )
}
