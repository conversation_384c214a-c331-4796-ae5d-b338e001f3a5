import Image from "next/image"
import Link from "next/link"
import { Phone } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gray-900 text-white">
        <div className="absolute inset-0 z-0">
          <Image
            src="/Transform_Your_Outdoor_Space__Professional_grading_excavation_hardscaping_and_landscaping_servic.webp"
            alt="Professional yard grading, excavation, hardscaping and landscaping services transforming outdoor spaces in Southeastern Pennsylvania"
            fill
            className="object-cover opacity-80"
            priority
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>
        <div className="container relative z-10 mx-auto px-4 py-24 sm:px-6 lg:px-8 lg:py-32">
          <div className="max-w-3xl">
            <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl">
              Transform Your Outdoor Space
            </h1>
            <p className="mt-6 text-xl text-gray-300">
              Professional yard grading, land leveling, excavation, hardscaping, and landscaping services to create the outdoor environment of
              your dreams.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4">
              <Button asChild size="lg" className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
                <Link href="/contact-us">Get Your Free Estimate</Link>
              </Button>
              <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
                <Link href="/services">Explore Our Services</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 bg-[#F3F1E7]">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">Our Comprehensive Outdoor Services</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              From ground preparation to beautiful finishing touches, we provide end-to-end solutions for your property.
            </p>
          </div>

          <div className="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3 justify-items-center">
            {[
              {
                title: "Yard Grading",
                description:
                  "Professional yard grading services to ensure proper drainage and foundation for your outdoor projects.",
                image:
                  "/yard_grading__Professional_land_yard_grading_services_to_ensure_proper_drainage_and_foundation_for_.webp",
                link: "/services/yard-grading",
              },
              {
                title: "Excavation",
                description:
                  "Expert excavation services for foundations, pools, and other outdoor features.",
                image:
                  "/Excavation__Expert_excavation_services_for_foundations_pools_and_other_outdoor_features_(1).webp",
                link: "/services/excavation",
              },
              {
                title: "Land Leveling",
                description:
                  "Expert land leveling to create the perfect base for patios, walkways, and other outdoor features.",
                image:
                  "/lawn_leveling__Expert_ground_lawn_leveling_to_create_the_perfect_base_for_patios_walkways_and_oth.webp",
                link: "/services/land-leveling",
              },
              {
                title: "Hardscaping",
                description:
                  "Beautiful and durable hardscape installations including patios, walkways, retaining walls, and more.",
                image:
                  "/Hardscaping__Beautiful_and_durable_hardscape_installations_including_patios_walkways_retaining_wa_(1).webp",
                link: "/services/hardscaping",
              },
              {
                title: "Landscaping",
                description:
                  "Complete landscaping services including sod installation, planting, mulching, and landscape design.",
                image:
                  "/Landscaping__Complete_landscaping_services_including_sod_installation_planting_mulching_and_land_(1).webp",
                link: "/services/landscaping",
              },
            ].map((service, index) => (
              <Link key={index} href={service.link} className="group">
                <Card className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative h-48">
                    <Image
                      src={service.image || "/placeholder.svg"}
                      alt={`Professional ${service.title.toLowerCase()} services by 3S Builder Solution in Southeastern Pennsylvania`}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-[#4A6C6F] transition-colors">{service.title}</h3>
                    <p className="mt-2 text-gray-600">{service.description}</p>
                    <div className="mt-4">
                      <span className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
                        Learn More →
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

     

      {/* Process Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">Our Process</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              We follow a proven process to ensure your project is completed to the highest standards.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              {
                step: "1",
                title: "Consultation",
                description: "We meet to discuss your vision, needs, and budget for your outdoor project.",
                icon: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01",
              },
              {
                step: "2",
                title: "Design & Planning",
                description: "We create a detailed plan tailored to your property's specific conditions.",
                icon: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",
              },
              {
                step: "3",
                title: "Professional Installation",
                description: "Our experienced team executes the plan with attention to every detail.",
                icon: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z",
              },
              {
                step: "4",
                title: "Final Walkthrough",
                description: "We review the completed project together to ensure your complete satisfaction.",
                icon: "M5 13l4 4L19 7",
              },
            ].map((step, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-6 border-t-4 border-[#4A6C6F]">
                <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-[#709CA7]/20 text-[#4A6C6F] mb-4">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={step.icon} />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-2">
                  <span className="text-[#4A6C6F] mr-2">{step.step}.</span>
                  {step.title}
                </h3>
                <p className="text-gray-700">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-[#F3F1E7]">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">What Our Clients Say</h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              Don't just take our word for it - hear from our satisfied customers.
            </p>
          </div>

          <div className="mt-12 grid gap-8 md:grid-cols-3">
            {[
              {
                quote:
                  "The team transformed our backyard into a beautiful outdoor living space. Their attention to detail and professionalism exceeded our expectations.",
                author: "Michael R.",
                location: "Philadelphia, PA",
                image: "/placeholder-user.jpg",
              },
              {
                quote:
                  "The yard grading work they did solved our drainage issues completely. Very knowledgeable team and reasonable pricing.",
                author: "Jennifer T.",
                location: "Bensalem, PA",
                image: "/placeholder-user.jpg",
              },
              {
                quote:
                  "From the initial consultation to the final walkthrough, working with this team was a pleasure. Highly recommend their hardscaping services!",
                author: "David M.",
                location: "Abington, PA",
                image: "/placeholder-user.jpg",
              },
            ].map((testimonial, index) => (
              <div key={index} className="p-6 bg-[#F3F1E7] rounded-lg shadow-sm">
                <div className="flex h-10 w-10 mb-4">
                  <svg className="h-10 w-10 text-[#4A6C6F]" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                </div>
                <p className="text-gray-600 italic mb-4">{testimonial.quote}</p>
                <div>
                  <p className="font-medium text-gray-900">{testimonial.author}</p>
                  <p className="text-gray-500">{testimonial.location}</p>
                </div>
              </div>
            ))}
          </div>

        </div>
      </section>

      {/* Areas We Serve */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Serving Communities Across Southeastern Pennsylvania
            </h2>
            <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
              We provide our expert services to homeowners and businesses throughout the region.
            </p>
          </div>

          <div className="mt-12 relative h-[400px] rounded-lg overflow-hidden">
            <Image
              src="/Serving_Communities_Across_Southeastern_Pennsylvania__We_provide_our_expert_services_to_homeowners_.webp"
              alt="Map of Southeastern Pennsylvania"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="text-center p-8 bg-white bg-opacity-90 rounded-lg max-w-2xl">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Service Areas Include:</h3>
                <div className="flex flex-wrap justify-center gap-2 mb-6">
                  {[
                    "Philadelphia",
                    "Bensalem",
                    "Abington",
                    "Warminster",
                    "Mount Airy",
                    "Horsham",
                    "Springfield Township",
                    "Lansdale",
                  ].map((location, index) => (
                    <Link
                      key={index}
                      href={`/locations/${location.toLowerCase().replace(" ", "-")}-pa`}
                      className="px-4 py-2 bg-[#F3F1E7] rounded-full text-[#4A6C6F] hover:bg-[#F3F1E7]/80 border border-[#709CA7]"
                    >
                      {location}
                    </Link>
                  ))}
                </div>
                <Button asChild className="bg-[#4A6C6F] hover:bg-[#3a5a5d] text-white">
                  <Link href="/locations">View All Service Areas</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-16 bg-[#4A6C6F] text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold sm:text-4xl">Ready to Transform Your Outdoor Space?</h2>
          <p className="mt-4 text-lg text-white/90 max-w-3xl mx-auto">
            Contact us today to schedule your free consultation and estimate.
          </p>
          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-[#709CA7] text-white hover:bg-[#3a5a5d]">
              <Link href="/contact-us">Request Your Free Estimate</Link>
            </Button>
            <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
              <a href="tel:4132414577" className="flex items-center gap-2">
                <Phone size={20} />
                <span>************</span>
              </a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
