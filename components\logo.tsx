interface LogoProps {
  variant?: "default" | "inverted"
  size?: "sm" | "md" | "lg"
  type?: "shield" | "circular" | "horizontal" | "minimalist"
}

export default function Logo({ variant = "default", size = "md", type = "shield" }: LogoProps) {
  const isInverted = variant === "inverted"
  const primaryColor = isInverted ? "#F3F1E7" : "#4A6C6F"
  const secondaryColor = isInverted ? "#F3F1E7" : "#709CA7"
  const textColor = isInverted ? "#F3F1E7" : "#4A6C6F"

  const sizeClass = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-16 h-16",
  }[size]

  const LogoComponent = {
    shield: ShieldLogo,
    circular: CircularLogo,
    horizontal: HorizontalLogo,
    minimalist: MinimalistLogo,
  }[type]

  return (
    <div className={sizeClass}>
      <LogoComponent inverted={isInverted} />
    </div>
  )
}

function ShieldLogo({ inverted = false }: { inverted?: boolean }) {
  const primaryColor = inverted ? "#F3F1E7" : "#4A6C6F"
  const secondaryColor = inverted ? "#F3F1E7" : "#709CA7"
  const textColor = inverted ? "#F3F1E7" : "#4A6C6F"

  return (
    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      {/* Shield Shape */}
      <path
        d="M100 10 L180 50 L180 110 C180 150 145 180 100 190 C55 180 20 150 20 110 L20 50 Z"
        fill={secondaryColor}
        opacity="0.2"
      />
      <path d="M100 20 L170 55 L170 105 C170 140 140 165 100 175 C60 165 30 140 30 105 L30 55 Z" fill={primaryColor} />

      {/* 3S Text */}
      <text
        x="100"
        y="90"
        fontFamily="Arial, sans-serif"
        fontSize="50"
        fontWeight="bold"
        fill={inverted ? primaryColor : "#F3F1E7"}
        textAnchor="middle"
      >
        3S
      </text>

      {/* Shovel/Spade Icon */}
      <path d="M85 100 L115 100 L110 140 L90 140 Z" fill={inverted ? primaryColor : "#F3F1E7"} />
      <path d="M95 140 L105 140 L103 160 L97 160 Z" fill={inverted ? primaryColor : "#F3F1E7"} />

      {/* Company Name */}
      <text
        x="100"
        y="120"
        fontFamily="Arial, sans-serif"
        fontSize="12"
        fontWeight="bold"
        fill={inverted ? primaryColor : "#F3F1E7"}
        textAnchor="middle"
      >
        BUILDER SOLUTION
      </text>
    </svg>
  )
}

function CircularLogo({ inverted = false }: { inverted?: boolean }) {
  const primaryColor = inverted ? "#F3F1E7" : "#4A6C6F"
  const secondaryColor = inverted ? "#F3F1E7" : "#709CA7"

  return (
    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      {/* Outer Circle */}
      <circle cx="100" cy="100" r="90" fill={secondaryColor} opacity="0.2" />

      {/* Inner Circle */}
      <circle cx="100" cy="100" r="75" fill={primaryColor} />

      {/* 3S Text */}
      <text
        x="100"
        y="90"
        fontFamily="Arial, sans-serif"
        fontSize="45"
        fontWeight="bold"
        fill={inverted ? primaryColor : "#F3F1E7"}
        textAnchor="middle"
      >
        3S
      </text>

      {/* Landscape Element */}
      <path
        d="M60 120 L85 100 L100 110 L115 100 L140 120"
        stroke={inverted ? primaryColor : "#F3F1E7"}
        strokeWidth="5"
        fill="none"
      />

      {/* Company Name */}
      <text
        x="100"
        y="140"
        fontFamily="Arial, sans-serif"
        fontSize="12"
        fontWeight="bold"
        fill={inverted ? primaryColor : "#F3F1E7"}
        textAnchor="middle"
      >
        BUILDER SOLUTION
      </text>
    </svg>
  )
}

function HorizontalLogo({ inverted = false }: { inverted?: boolean }) {
  const primaryColor = inverted ? "#F3F1E7" : "#4A6C6F"
  const secondaryColor = inverted ? "#F3F1E7" : "#709CA7"
  const textColor = inverted ? "#F3F1E7" : "#4A6C6F"

  return (
    <svg viewBox="0 0 300 100" xmlns="http://www.w3.org/2000/svg">
      {/* Icon Background */}
      <rect x="10" y="10" width="80" height="80" rx="10" fill={primaryColor} />

      {/* 3S Text in Icon */}
      <text
        x="50"
        y="65"
        fontFamily="Arial, sans-serif"
        fontSize="40"
        fontWeight="bold"
        fill={inverted ? primaryColor : "#F3F1E7"}
        textAnchor="middle"
      >
        3S
      </text>

      {/* Company Name */}
      <text
        x="200"
        y="45"
        fontFamily="Arial, sans-serif"
        fontSize="24"
        fontWeight="bold"
        fill={textColor}
        textAnchor="middle"
      >
        3S BUILDER
      </text>

      <text
        x="200"
        y="75"
        fontFamily="Arial, sans-serif"
        fontSize="24"
        fontWeight="bold"
        fill={textColor}
        textAnchor="middle"
      >
        SOLUTION
      </text>

      {/* Connecting Line */}
      <line x1="110" y1="50" x2="130" y2="50" stroke={secondaryColor} strokeWidth="3" />
    </svg>
  )
}

function MinimalistLogo({ inverted = false }: { inverted?: boolean }) {
  const primaryColor = inverted ? "#F3F1E7" : "#4A6C6F"
  const secondaryColor = inverted ? "#F3F1E7" : "#709CA7"
  const textColor = inverted ? "#F3F1E7" : "#4A6C6F"

  return (
    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      {/* Three Triangular Elements */}
      <polygon points="60,120 100,60 140,120" fill={primaryColor} />

      <polygon points="80,120 100,80 120,120" fill={secondaryColor} opacity="0.7" />

      <polygon points="90,120 100,100 110,120" fill={inverted ? primaryColor : "#F3F1E7"} />

      {/* 3S Text */}
      <text
        x="100"
        y="150"
        fontFamily="Arial, sans-serif"
        fontSize="30"
        fontWeight="bold"
        fill={textColor}
        textAnchor="middle"
      >
        3S
      </text>

      {/* Company Name */}
      <text
        x="100"
        y="175"
        fontFamily="Arial, sans-serif"
        fontSize="12"
        fontWeight="bold"
        fill={textColor}
        textAnchor="middle"
      >
        BUILDER SOLUTION
      </text>
    </svg>
  )
}
