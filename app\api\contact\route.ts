import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import nodemailer from 'nodemailer';

// Rate limiting storage (in production, use Redis or database)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Simple rate limiting function
function rateLimit(ip: string, limit: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(ip);

  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= limit) {
    return false;
  }

  record.count++;
  return true;
}

// Input validation and sanitization
function validateAndSanitizeInput(data: any) {
  const errors: string[] = [];

  // Required fields validation
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }

  if (!data.email || typeof data.email !== 'string') {
    errors.push('Email is required');
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      errors.push('Please provide a valid email address');
    }
  }

  // Optional phone validation
  if (data.phone && typeof data.phone === 'string' && data.phone.trim().length > 0) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = data.phone.replace(/[\s\-\(\)\.]/g, '');
    if (!phoneRegex.test(cleanPhone)) {
      errors.push('Please provide a valid phone number');
    }
  }

  // Sanitize inputs
  const sanitized = {
    name: data.name?.toString().trim().slice(0, 100) || '',
    email: data.email?.toString().trim().toLowerCase().slice(0, 254) || '',
    phone: data.phone?.toString().trim().slice(0, 20) || '',
    service: data.service?.toString().trim().slice(0, 100) || '',
    message: data.message?.toString().trim().slice(0, 2000) || ''
  };

  return { errors, sanitized };
}

// Email sending function with multiple transport options
async function sendEmail(formData: any) {
  const emailContent = `
New Contact Form Submission from 3S Builder Solution Website

Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone || 'Not provided'}
Service Interested In: ${formData.service || 'Not specified'}

Message:
${formData.message || 'No message provided'}

---
Submitted at: ${new Date().toLocaleString()}
IP Address: ${formData.ip}
User Agent: ${formData.userAgent}
  `;

  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4A6C6F;">New Contact Form Submission</h2>
      <div style="background: #f9f9f9; padding: 20px; border-radius: 8px;">
        <p><strong>Name:</strong> ${formData.name}</p>
        <p><strong>Email:</strong> ${formData.email}</p>
        <p><strong>Phone:</strong> ${formData.phone || 'Not provided'}</p>
        <p><strong>Service Interested In:</strong> ${formData.service || 'Not specified'}</p>
        <div style="margin-top: 20px;">
          <strong>Message:</strong>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${formData.message ? formData.message.replace(/\n/g, '<br>') : 'No message provided'}
          </div>
        </div>
      </div>
      <div style="margin-top: 20px; font-size: 12px; color: #666;">
        <p>Submitted at: ${new Date().toLocaleString()}</p>
        <p>IP Address: ${formData.ip}</p>
      </div>
    </div>
  `;

  try {
    // Create transporter based on environment
    let transporter;

    if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
      // Use SMTP configuration
      transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      });
    } else {
      // Fallback to Gmail (for development/testing)
      // Note: In production, use proper SMTP service
      console.log('No SMTP configuration found, using console logging');
      console.log('Email content:', emailContent);
      return { success: true, messageId: `dev_${Date.now()}` };
    }

    const mailOptions = {
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: process.env.CONTACT_EMAIL || '<EMAIL>',
      subject: `New Contact Form Submission - ${formData.service || 'General Inquiry'}`,
      text: emailContent,
      html: htmlContent,
      replyTo: formData.email,
    };

    const result = await transporter.sendMail(mailOptions);
    return { success: true, messageId: result.messageId };

  } catch (error) {
    console.error('Email sending error:', error);
    // Log the email content for manual processing
    console.log('Failed email content:', emailContent);
    throw new Error('Failed to send email');
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const headersList = await headers();
    const forwarded = headersList.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown';

    // Apply rate limiting
    if (!rateLimit(ip)) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate and sanitize input
    const { errors, sanitized } = validateAndSanitizeInput(body);

    if (errors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: errors },
        { status: 400 }
      );
    }

    // Add metadata for email
    const formDataWithMeta = {
      ...sanitized,
      ip,
      userAgent: headersList.get('user-agent') || 'Unknown',
      timestamp: new Date().toISOString()
    };

    // Send email
    const emailResult = await sendEmail(formDataWithMeta);

    if (!emailResult.success) {
      throw new Error('Failed to send email');
    }

    // Log successful submission (in production, use proper logging)
    console.log('Contact form submission successful:', {
      name: sanitized.name,
      email: sanitized.email,
      service: sanitized.service,
      timestamp: formDataWithMeta.timestamp
    });

    return NextResponse.json({
      success: true,
      message: 'Thank you for your message. We will get back to you soon!',
      messageId: emailResult.messageId
    });

  } catch (error) {
    console.error('Contact form error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    );
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
