import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export const metadata = {
  title: "Professional Hardscaping Services in Southeastern PA | 3S Builder Solution",
  description:
    "Expert hardscaping services including patios, walkways, retaining walls, and more. Serving Philadelphia, Bensalem, and surrounding areas in Southeastern PA.",
}

export default function HardscapingPage() {
  return (
    <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="relative rounded-lg overflow-hidden mb-12">
        <div className="absolute inset-0 z-0">
          <Image
            src="/Professional_Hardscaping_Services_in_Southeastern_PA__Transform_your_outdoor_space_with_beautiful_.webp"
            alt="Professional hardscaping service"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50"></div>
        </div>
        <div className="relative z-10 p-8 md:p-12 lg:p-16 text-white">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">
            Professional Hardscaping Services in Southeastern PA
          </h1>
          <p className="text-lg md:text-xl max-w-3xl mb-8">
            Transform your outdoor space with beautiful, functional hardscape features built to last.
          </p>
          <Button asChild size="lg" className="bg-[#4A6C6F] hover:bg-[#3a5a5d]">
            <Link href="/contact-us">Get a Free Estimate</Link>
          </Button>
        </div>
      </div>

      {/* Introduction */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Expert Hardscaping Services</h2>
        <p className="text-lg text-gray-700 mb-4">
          Hardscaping refers to the non-living elements of landscape design, including patios, walkways, retaining
          walls, and other structures that create functional and beautiful outdoor spaces. Well-designed hardscaping not
          only enhances the appearance of your property but also increases its usability and value.
        </p>
        <p className="text-lg text-gray-700">
          At 3S Builder Solution, we specialize in creating custom hardscape features that complement your home's
          architecture and your personal style. Our experienced team handles every aspect of the process, from initial
          design to proper ground preparation and expert installation. With our attention to detail and quality
          craftsmanship, we deliver hardscape elements that are both beautiful and built to last.
        </p>
      </div>

      {/* Types of Hardscaping Services */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Hardscaping Services We Offer</h2>
        <div className="grid md:grid-cols-2 gap-8">
          {[
            {
              title: "Patios & Outdoor Living Spaces",
              description:
                "Custom-designed patios using pavers, natural stone, or concrete to create beautiful outdoor living and entertainment areas.",
              image: "/Hardscaping_Services_We_Offer__Patios__Outdoor_Living_Spaces.webp",
            },
            {
              title: "Walkways & Paths",
              description:
                "Functional and attractive walkways that connect different areas of your property and enhance its overall design.",
              image: "/Hardscaping_Services_We_Offer__Walkways__Paths.webp",
            },
            {
              title: "Retaining Walls",
              description:
                "Structural walls that manage elevation changes, prevent erosion, and create usable space on sloped properties.",
              image: "/Hardscaping_Services_We_Offer__Retaining_Walls.webp",
            },
            {
              title: "Steps & Staircases",
              description:
                "Custom outdoor steps and staircases that provide safe, convenient access between different levels of your property.",
              image: "/Hardscaping_Services_We_Offer__Steps__Staircases.webp",
            },
            {
              title: "Fire Pits & Outdoor Fireplaces",
              description:
                "Cozy gathering spots that extend the usability of your outdoor space into cooler months and evenings.",
              image: "/Hardscaping_Services_We_Offer__Fire_Pits__Outdoor_Fireplaces.webp",
            },
            {
              title: "Outdoor Kitchens",
              description:
                "Functional cooking and dining areas that bring the convenience of your kitchen to your outdoor living space.",
              image: "/Hardscaping_Services_We_Offer__Outdoor_Kitchens.webp",
            },
          ].map((service, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="relative h-48">
                <Image src={service.image || "/placeholder.svg"} alt={service.title} fill className="object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">{service.title}</h3>
                <p className="text-gray-700">{service.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Materials We Use */}
      <div className="mb-12 bg-[#F3F1E7] rounded-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Quality Hardscaping Materials</h2>
        <p className="text-lg text-gray-700 mb-6">
          At 3S Builder Solution, we use only high-quality materials that ensure durability, beauty, and long-term
          performance for your hardscaping projects. We offer a wide range of options to match your aesthetic
          preferences and functional needs.
        </p>
        <div className="grid md:grid-cols-3 gap-6">
          {[
            {
              name: "Concrete Pavers",
              description:
                "Versatile, durable, and available in countless colors, shapes, and textures for patios, walkways, and driveways.",
              image: "/Quality_Hardscaping_Materials__Concrete_Pavers.webp",
            },
            {
              name: "Natural Stone",
              description:
                "Beautiful, timeless materials like flagstone, bluestone, and limestone that add organic character to any hardscape project.",
              image: "/Quality_Hardscaping_Materials__Natural_Stone.webp",
            },
            {
              name: "Retaining Wall Blocks",
              description:
                "Engineered blocks designed specifically for creating strong, attractive retaining walls of various heights and styles.",
              image: "/Quality_Hardscaping_Materials__Retaining_Wall_Blocks.webp",
            },
          ].map((material, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="relative h-48">
                <Image src={material.image || "/placeholder.svg"} alt={material.name} fill className="object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">{material.name}</h3>
                <p className="text-gray-700">{material.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Our Hardscaping Process */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Our Professional Hardscaping Process</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {[
            {
              step: "1",
              title: "Consultation & Design",
              description:
                "We begin with a thorough consultation to understand your vision, needs, and budget. Our team then creates a custom design for your hardscape project.",
            },
            {
              step: "2",
              title: "Site Preparation",
              description:
                "We properly prepare the site, including excavation, yard-grading, and establishing a solid foundation for your hardscape features.",
            },
            {
              step: "3",
              title: "Base Installation",
              description:
                "We install a proper base of crushed stone or gravel, ensuring adequate depth and compaction for stability and drainage.",
            },
            {
              step: "4",
              title: "Material Installation",
              description:
                "Our skilled team carefully installs your chosen materials, whether pavers, natural stone, or wall blocks, with precision and attention to detail.",
            },
            {
              step: "5",
              title: "Jointing & Finishing",
              description:
                "We apply appropriate jointing materials between pavers or stones and add finishing touches like edge restraints and caps.",
            },
            {
              step: "6",
              title: "Final Inspection & Cleanup",
              description:
                "We thoroughly inspect the completed work, make any necessary adjustments, and clean the site before final approval.",
            },
          ].map((step, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 border-t-4 border-[#4A6C6F]">
              <div className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-[#709CA7]/20 text-[#4A6C6F] font-bold text-lg mb-4">
                {step.step}
              </div>
              <h3 className="text-xl font-bold mb-2">{step.title}</h3>
              <p className="text-gray-700">{step.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits of Professional Hardscaping */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Benefits of Professional Hardscaping</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div className="relative rounded-lg overflow-hidden h-[500px]">
            <Image
              src="/Benefits_of_Professional_Hardscaping__Beautiful_hardscaping_project.webp"
              alt="Beautiful hardscaping project"
              fill
              className="object-cover"
            />
          </div>
          <div>
            <ul className="space-y-4">
              {[
                {
                  title: "Increased Property Value",
                  description:
                    "Well-designed hardscaping can significantly increase your home's market value, offering an excellent return on investment.",
                },
                {
                  title: "Extended Living Space",
                  description:
                    "Hardscaped areas create functional outdoor rooms that effectively expand your home's usable living space.",
                },
                {
                  title: "Reduced Maintenance",
                  description:
                    "Compared to lawns and gardens, hardscaped areas require minimal ongoing maintenance while maintaining their beauty.",
                },
                {
                  title: "Improved Drainage & Erosion Control",
                  description:
                    "Properly installed hardscaping helps manage water flow, preventing erosion and water damage to your property.",
                },
                {
                  title: "Enhanced Outdoor Entertainment",
                  description:
                    "Patios, fire pits, and outdoor kitchens create perfect spaces for gathering with family and friends.",
                },
                {
                  title: "Year-Round Usability",
                  description:
                    "Hardscaped areas remain usable in various weather conditions, extending your enjoyment of outdoor spaces.",
                },
              ].map((item, index) => (
                <li key={index} className="flex">
                  <div className="flex-shrink-0 h-6 w-6 mt-1 mr-3 flex items-center justify-center rounded-full bg-[#709CA7]/20 text-[#4A6C6F]">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Areas We Serve */}
      <div className="mb-12 bg-[#F3F1E7] rounded-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Areas We Serve</h2>
        <p className="text-lg text-gray-700 mb-6">
          3S Builder Solution provides professional hardscaping services throughout Southeastern Pennsylvania, including
          these primary service areas:
        </p>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {[
            "Philadelphia",
            "Bensalem",
            "Abington",
            "Warminster",
            "Mount Airy",
            "Horsham",
            "Springfield Township",
            "Lansdale",
            "Southampton",
            "Willow Grove",
            "Chestnut Hill",
            "Bristol",
            "Hatboro",
          ].map((location, index) => (
            <Link
              key={index}
              href={`/locations/${location.toLowerCase().replace(" ", "-")}-pa`}
              className="bg-white p-3 rounded-lg text-center shadow-sm hover:shadow-md transition-shadow text-[#4A6C6F] hover:text-[#3a5a5d]"
            >
              {location}, PA
            </Link>
          ))}
        </div>
        <div className="mt-6 text-center">
          <Button asChild variant="outline" className="text-[#4A6C6F] border-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/locations">View All Service Areas</Link>
          </Button>
        </div>
      </div>

      {/* FAQs */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Frequently Asked Questions About Hardscaping</h2>
        <Accordion type="single" collapsible className="w-full">
          {[
            {
              question: "How much does hardscaping typically cost?",
              answer:
                "Hardscaping costs vary widely depending on the materials chosen, project size, site conditions, and design complexity. Basic paver patios might start at $15-$25 per square foot, while more elaborate designs with premium materials can range from $30-$50+ per square foot. Retaining walls typically cost $40-$100+ per linear foot depending on height and materials. During your consultation, we'll provide a detailed estimate based on your specific project requirements.",
            },
            {
              question: "How long do hardscape features last?",
              answer:
                "When properly installed by professionals, hardscape features are extremely durable. Concrete pavers typically last 25-50 years or more. Natural stone can last a lifetime with minimal maintenance. Retaining walls, when correctly built, should last 20-40+ years depending on conditions. The key to longevity is proper installation with adequate base preparation, drainage, and quality materials - all standards we adhere to at 3S Builder Solution.",
            },
            {
              question: "What maintenance do hardscape features require?",
              answer:
                "Hardscaping requires significantly less maintenance than landscaping, but some upkeep helps maintain appearance and longevity. Typical maintenance includes: 1) Occasional sweeping or rinsing to remove debris, 2) Annual inspection of joints and edges, with sand replenishment if needed, 3) Periodic sealing (optional but beneficial for certain materials), 4) Weed control in joints as needed, and 5) Checking for any settling or movement. We can provide specific maintenance recommendations for your particular hardscape features.",
            },
            {
              question: "Do I need permits for hardscaping projects?",
              answer:
                "Permit requirements vary by municipality and project type. Generally, small patios and walkways at ground level may not require permits, while larger projects, retaining walls over a certain height (typically 2-4 feet), and any work affecting drainage patterns often do require permits. As part of our service, we can help determine if permits are needed for your specific project and assist with the application process if required.",
            },
            {
              question: "Can hardscaping be installed in winter?",
              answer:
                "While we can perform some hardscaping work year-round, there are limitations during winter months. Frozen ground, snow cover, and very cold temperatures can affect excavation, proper base compaction, and material setting. The ideal seasons for hardscaping installation are spring, summer, and fall when temperatures are moderate and ground conditions are workable. However, we can discuss your specific timeline and determine what might be possible during winter months if needed.",
            },
          ].map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left font-medium text-lg">{faq.question}</AccordionTrigger>
              <AccordionContent className="text-gray-700">{faq.answer}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>

      {/* Related Services */}
      <div className="mb-12">
        <h2 className="text-2xl md:text-3xl font-bold mb-6">Related Services</h2>
        <p className="text-lg text-gray-700 mb-6">
          Hardscaping is often part of a complete outdoor transformation. Explore our other services that complement our
          hardscaping work:
        </p>
        <div className="grid md:grid-cols-4 gap-6">
          {[
            {
              title: "Yard Grading",
              description: "Professional yard grading to ensure proper slopes and drainage for your property.",
              link: "/services/yard-grading",
            },
            {
              title: "Excavation",
              description: "Precise excavation services for foundations, utilities, drainage systems, and more.",
              link: "/services/excavation",
            },
            {
              title: "Land Leveling",
              description: "Precise land leveling for patios, walkways, and other flat surfaces.",
              link: "/services/land-leveling",
            },
            {
              title: "Landscaping",
              description: "Complete landscape design and installation services to beautify your property.",
              link: "/services/landscaping",
            },
          ].map((service, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-xl font-bold mb-2">{service.title}</h3>
              <p className="text-gray-700 mb-4">{service.description}</p>
              <Link href={service.link} className="text-[#4A6C6F] hover:text-[#3a5a5d] font-medium">
                Learn more →
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* CTA */}
      <div className="bg-[#4A6C6F] rounded-lg p-8 text-white text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Transform Your Outdoor Space?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          Contact 3S Builder Solution today for a free consultation and estimate. Our professional team is ready to help
          you create beautiful, functional hardscape features for your property.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7]">
            <Link href="/contact-us">Get a Free Estimate</Link>
          </Button>
          <Button asChild size="lg" className="bg-white text-[#4A6C6F] hover:bg-[#F3F1E7] border-2 border-white">
            <a href="tel:4132414577" className="flex items-center gap-2">
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <span>************</span>
            </a>
          </Button>
        </div>
      </div>
    </div>
  )
}
