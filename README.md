# 3S Builder Solution Website

A modern, responsive website for 3S Builder Solution - a professional yard grading, excavation, land leveling, hardscaping, and landscaping company serving Southeastern Pennsylvania.

## 🚀 Live Website

**Production:** [https://www.3sbuildersolution.com](https://www.3sbuildersolution.com)

## 📋 Table of Contents

- [Features](#features)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Getting Started](#getting-started)
- [Development](#development)
- [Security Features](#security-features)
- [Deployment](#deployment)
- [Environment Variables](#environment-variables)
- [Performance](#performance)
- [SEO Optimization](#seo-optimization)
- [Contributing](#contributing)
- [License](#license)

## ✨ Features

### Core Features
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Service Pages**: Dedicated pages for each service offering
- **Location Pages**: SEO-optimized pages for different service areas
- **Contact Forms**: Integrated contact and estimate request forms
- **Performance Optimized**: Fast loading with Next.js optimization
- **SEO Friendly**: Comprehensive meta tags, sitemap, and robots.txt
- **Accessibility**: WCAG compliant with proper ARIA labels

### Business Features
- **Service Showcase**: Yard grading, excavation, land leveling, hardscaping, landscaping
- **Location Coverage**: Multiple Pennsylvania locations
- **Free Estimates**: Easy-to-use estimate request system with email notifications
- **Contact Forms**: Dual-form system (external + backup) with full email integration
- **Project Gallery**: Before/after project showcases
- **Company Information**: About us, terms of service, privacy policy

## 🛠 Technology Stack

### Frontend
- **Framework**: [Next.js 15.5.2](https://nextjs.org/) (React 18)
- **Styling**: [Tailwind CSS 3.4.17](https://tailwindcss.com/)
- **UI Components**: [Radix UI](https://www.radix-ui.com/)
- **Icons**: [Lucide React](https://lucide.dev/)
- **Typography**: Custom fonts with Next.js font optimization

### Development Tools
- **Language**: TypeScript 5
- **Linting**: ESLint with Next.js configuration
- **Package Manager**: npm
- **Build Tool**: Next.js built-in bundler
- **Bundle Analyzer**: @next/bundle-analyzer

### External Services
- **Forms**: Dual system - OpnForm (primary) + Custom API (backup)
- **Email**: Nodemailer with SMTP support
- **Hosting**: Vercel (recommended)
- **Domain**: Custom domain with SSL

## 📁 Project Structure

```
3s-builder-solution/
├── app/                          # Next.js App Router
│   ├── about-us/                 # About page
│   ├── api/                      # API routes (currently empty)
│   ├── contact-us/               # Contact page with forms
│   ├── images/                   # Image assets
│   ├── locations/                # Location-specific pages
│   │   ├── abington-pa/
│   │   ├── bensalem-pa/
│   │   ├── bristol-pa/
│   │   └── [other-locations]/
│   ├── privacy-policy/           # Privacy policy page
│   ├── projects/                 # Project showcase
│   ├── services/                 # Service pages
│   │   ├── excavation/
│   │   ├── hardscaping/
│   │   ├── land-leveling/
│   │   ├── landscaping/
│   │   └── yard-grading/
│   ├── terms-of-service/         # Terms of service
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   ├── page.tsx                  # Homepage
│   ├── robots.ts                 # Robots.txt generation
│   └── sitemap.ts                # Sitemap generation
├── components/                   # Reusable components
│   ├── ui/                       # UI components (Radix-based)
│   ├── FreeEstimateForm.tsx      # Contact form component
│   ├── cta-section.tsx           # Call-to-action sections
│   ├── footer.tsx                # Site footer
│   ├── header.tsx                # Site header/navigation
│   ├── location-grid.tsx         # Location display grid
│   ├── logo.tsx                  # Company logo component
│   ├── performance-tracker.tsx   # Performance monitoring
│   ├── service-card.tsx          # Service display cards
│   └── theme-provider.tsx        # Theme context provider
├── lib/                          # Utility libraries
│   ├── performance.ts            # Performance utilities
│   ├── services-data.ts          # Service information data
│   └── utils.ts                  # General utilities
├── public/                       # Static assets
│   ├── [images]/                 # Optimized WebP images
│   └── favicon files             # Favicon and app icons
├── styles/                       # Additional styles
│   └── globals.css               # Legacy global styles
├── .env.example                  # Environment variables template
├── .gitignore                    # Git ignore rules
├── components.json               # Shadcn/ui configuration
├── next.config.mjs               # Next.js configuration
├── package.json                  # Dependencies and scripts
├── postcss.config.mjs            # PostCSS configuration
├── tailwind.config.ts            # Tailwind CSS configuration
├── tsconfig.json                 # TypeScript configuration
├── update-location-pages.js      # Location page update script
└── vercel.json                   # Vercel deployment configuration
```

## 🚀 Getting Started

### Prerequisites

- **Node.js**: Version 18.0 or higher
- **npm**: Version 8.0 or higher (comes with Node.js)
- **Git**: For version control

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/master4367/3s-builder-solution.git
   cd 3s-builder-solution
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your specific values
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run analyze      # Analyze bundle size

# Utilities
node update-location-pages.js  # Update location page metadata
```

## 🔧 Development

### Code Style and Standards

- **TypeScript**: Strict mode enabled for type safety
- **ESLint**: Next.js recommended configuration
- **Prettier**: Code formatting (configure in your editor)
- **Component Structure**: Functional components with hooks
- **File Naming**: kebab-case for files, PascalCase for components

### Adding New Pages

1. **Service Pages**: Create in `app/services/[service-name]/page.tsx`
2. **Location Pages**: Create in `app/locations/[location-name]/page.tsx`
3. **General Pages**: Create in `app/[page-name]/page.tsx`

### Component Development

```typescript
// Example component structure
import { ComponentProps } from 'react'

interface MyComponentProps {
  title: string
  description?: string
}

export default function MyComponent({ title, description }: MyComponentProps) {
  return (
    <div className="component-wrapper">
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </div>
  )
}
```

### Styling Guidelines

- **Tailwind CSS**: Use utility classes for styling
- **Responsive Design**: Mobile-first approach
- **Color Scheme**: Brand colors defined in tailwind.config.ts
- **Typography**: Consistent font sizes and line heights

## 🔒 Security Features

### Implemented Security Measures

1. **HTTP Security Headers**
   - Content Security Policy (CSP)
   - X-Frame-Options: DENY
   - X-Content-Type-Options: nosniff
   - Strict-Transport-Security (HSTS)
   - Referrer-Policy: strict-origin-when-cross-origin

2. **Form Security**
   - Iframe sandboxing for external forms
   - Input validation and sanitization
   - Error handling and fallback options
   - Cross-origin resource sharing (CORS) controls

3. **Data Protection**
   - No sensitive data in client-side code
   - Environment variables for configuration
   - Secure external service integration

4. **Performance Security**
   - Bundle analysis for dependency security
   - Optimized images to prevent attacks
   - Rate limiting considerations for production

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect to Vercel**
   ```bash
   # Install Vercel CLI
   npm i -g vercel

   # Login to Vercel
   vercel login

   # Deploy
   vercel
   ```

2. **Environment Variables**
   Set the following in Vercel dashboard:
   ```
   NEXT_PUBLIC_SITE_URL=https://your-domain.com
   NEXT_PUBLIC_SITE_NAME=3S Builder Solution
   NODE_ENV=production
   ```

3. **Custom Domain**
   - Add your domain in Vercel dashboard
   - Configure DNS records as instructed
   - SSL certificate is automatically provisioned

### Alternative Deployment Options

- **Netlify**: Compatible with static export
- **AWS Amplify**: Full-stack deployment option
- **Docker**: Containerized deployment
- **Traditional Hosting**: Static export with `npm run build`

## 🌍 Environment Variables

### Required Variables

```bash
# Site Configuration
NEXT_PUBLIC_SITE_URL=https://www.3sbuildersolution.com
NEXT_PUBLIC_SITE_NAME="3S Builder Solution"
NODE_ENV=production

# Contact Information
NEXT_PUBLIC_PHONE=************
NEXT_PUBLIC_EMAIL=<EMAIL>

# Form Service
NEXT_PUBLIC_OPNFORM_ENDPOINT=https://opnform.com/forms/free-estimate-form-afcau9

# Email Configuration (Required for contact form)
CONTACT_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
```

### Optional Variables

```bash
# Analytics
NEXT_PUBLIC_GA_ID=your-google-analytics-id
NEXT_PUBLIC_GTM_ID=your-google-tag-manager-id

# Social Media
NEXT_PUBLIC_FACEBOOK_URL=your-facebook-url
NEXT_PUBLIC_INSTAGRAM_URL=your-instagram-url
NEXT_PUBLIC_LINKEDIN_URL=your-linkedin-url
```

## ⚡ Performance

### Optimization Features

- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic route-based code splitting
- **Bundle Analysis**: Built-in bundle analyzer
- **Caching**: Aggressive caching strategies
- **Compression**: Gzip/Brotli compression
- **CDN**: Vercel Edge Network for global distribution

### Performance Monitoring

```bash
# Analyze bundle size
npm run analyze

# Lighthouse audit (in browser dev tools)
# - Performance: 90+
# - Accessibility: 95+
# - Best Practices: 90+
# - SEO: 95+
```

## 📧 Contact Form System

### Dual-Form Architecture

The website implements a robust dual-form system for maximum reliability:

#### Primary Form (OpnForm)
- **External Service**: OpnForm iframe integration
- **Features**: Advanced form builder with custom styling
- **Fallback**: Automatic detection of loading failures

#### Backup Form (Custom API)
- **Built-in Solution**: Next.js API route with email integration
- **Security Features**:
  - Input validation and sanitization
  - Rate limiting (5 requests per 15 minutes per IP)
  - CSRF protection
  - XSS prevention
- **Email Integration**: Nodemailer with SMTP support

### Email Configuration

#### Development
- Emails are logged to console for testing
- No SMTP configuration required

#### Production
Configure these environment variables:
```bash
SMTP_HOST=smtp.gmail.com          # Your SMTP server
SMTP_PORT=587                     # SMTP port (587 for TLS)
SMTP_SECURE=false                 # Use TLS (true for SSL)
SMTP_USER=<EMAIL>    # SMTP username
SMTP_PASS=your-app-password       # SMTP password/app password
SMTP_FROM=<EMAIL>  # From address
CONTACT_EMAIL=<EMAIL> # Destination email
```

#### Supported Email Providers
- **Gmail**: Use app passwords for authentication
- **SendGrid**: SMTP or API integration
- **Mailgun**: SMTP configuration
- **AWS SES**: SMTP credentials
- **Custom SMTP**: Any SMTP-compatible service

### Form Security Features

1. **Input Validation**
   - Required field validation
   - Email format validation
   - Phone number format validation
   - Maximum length limits

2. **Rate Limiting**
   - 5 submissions per 15 minutes per IP
   - Prevents spam and abuse
   - Configurable limits

3. **Data Sanitization**
   - HTML entity encoding
   - Input length restrictions
   - Special character handling

4. **Error Handling**
   - Graceful fallback to backup form
   - User-friendly error messages
   - Detailed logging for debugging

## 🔍 SEO Optimization

### Implemented SEO Features

1. **Meta Tags**: Comprehensive meta tags for all pages
2. **Structured Data**: JSON-LD schema markup
3. **Sitemap**: Dynamic sitemap generation
4. **Robots.txt**: Search engine crawling instructions
5. **Open Graph**: Social media sharing optimization
6. **Twitter Cards**: Twitter-specific meta tags
7. **Canonical URLs**: Prevent duplicate content issues

### SEO Best Practices

- **Page Titles**: Unique, descriptive titles for each page
- **Meta Descriptions**: Compelling descriptions under 160 characters
- **Header Structure**: Proper H1-H6 hierarchy
- **Image Alt Text**: Descriptive alt text for all images
- **Internal Linking**: Strategic internal link structure
- **Loading Speed**: Optimized for Core Web Vitals

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes**
4. **Test thoroughly**
   ```bash
   npm run build
   npm run start
   ```
5. **Submit a pull request**

### Code Review Guidelines

- Follow existing code style and patterns
- Include tests for new functionality
- Update documentation as needed
- Ensure all builds pass
- Test on multiple devices and browsers

## 📄 License

This project is proprietary software owned by 3S Builder Solution. All rights reserved.

## 📞 Support

For technical support or questions about this website:

- **Email**: <EMAIL>
- **Phone**: ************
- **Website**: [www.3sbuildersolution.com](https://www.3sbuildersolution.com)

---

**Built with ❤️ by the 3S Builder Solution team**
